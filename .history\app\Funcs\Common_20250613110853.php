<?php

use App\Exception\ValidateException;
use App\JsonRpc\MessageLogServiceInterface;
use Hyperf\Contract\StdoutLoggerInterface;
use Psr\Http\Server\MiddlewareInterface;
use Hyperf\HttpServer\Contract\ResponseInterface;
use Hyperf\HttpServer\Response;
use Hyperf\Utils\Context;
use Hyperf\Server\ServerFactory;
use Hyperf\Snowflake\IdGeneratorInterface;
use Hyperf\Utils\ApplicationContext;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Psr\Http\Message\ServerRequestInterface;
use Swoole\Websocket\Frame;
use Swoole\WebSocket\Server as WebSocketServer;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;
use App\JsonRpc\ConfigServiceInterface;
use App\Exception\BusinessException;
use App\Library\Facades\AdminService;
use App\JsonRpc\UniqueCodeLogServiceInterface;
use Hyperf\Contract\SessionInterface;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use Hyperf\HttpServer\Contract\RequestInterface;


/**
 * 容器实例
 */
if (!function_exists( 'container' )) {
    function container ()
    {
        return ApplicationContext::getContainer();
    }
}



/**
 * redis 客户端实例
 */
if (!function_exists( 'redis' )) {
    function redis ()
    {
        return container()->get( Redis::class );
    }
}

/**
 * server 实例 基于 swoole server
 */
if (!function_exists( 'server' )) {
    function server ()
    {
        return container()->get( ServerFactory::class )->getServer()->getServer();
    }
}

/**
 * server 实例 基于 swoole server
 */
if (!function_exists( 'admin' )) {
    function admin ()
    {
        return container()->get( \App\JsonRpc\AdminServiceInterface::class );
    }
}

/**
 * server 实例 基于 swoole server
 */
if (!function_exists( 'serialNoService' )) {
    function serialNoService ()
    {
        return container()->get( \App\JsonRpc\SerialNoServiceInterface::class );
    }
}

/**
 * websocket frame 实例
 */
if (!function_exists( 'frame' )) {
    function frame ()
    {
        return container()->get( Frame::class );
    }
}

/**
 * websocket 实例
 */
if (!function_exists( 'websocket' )) {
    function websocket ()
    {
        return container()->get( WebSocketServer::class );
    }
}

/**
 * 缓存实例 简单的缓存
 */
if (!function_exists( 'cache' )) {
    function cache ()
    {
        return container()->get( Psr\SimpleCache\CacheInterface::class );
    }
}
if (!function_exists( 'getRequestId' )) {
    function getRequestId()
    {
        $request = Context::get(ServerRequestInterface::class);
        $requestId = $request->getAttribute('request_id',uuid(false));
        return $requestId;
    }
}
/**
 * 控制台日志
 */
if (!function_exists( 'stdLog' )) {
    function stdLog ()
    {
        return container()->get( StdoutLoggerInterface::class );
    }
}

/**
 * 文件日志
 */
if (!function_exists( 'logger' )) {
    function logger ()
    {
        return \App\BaseLogger::getInstance() ;
    }
}

/**
 *
 */
if (!function_exists( 'request' )) {
    function request ()
    {
        return container()->get( ServerRequestInterface::class );
    }
}

/**
 *
 */
if (!function_exists( 'response' )) {
    function response ()
    {
        return container()->get( ResponseInterface::class );
    }
}

/**
 *
 */
if (!function_exists( 'validate' )) {
    function validate ()
    {
        return container()->get( ValidatorFactoryInterface::class );
    }
}

if (!function_exists( 'verifyIp' )) {
    function verifyIp ($realip)
    {
        return filter_var( $realip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 );
    }
}

if (!function_exists( 'getClientIp' )) {
    function getClientIp ()
    {
        /**
         * @var ServerRequestInterface $request
         */
        $request = \Hyperf\Utils\Context::get( ServerRequestInterface::class );
        $ip_addr = $request->getHeaderLine( 'x-forwarded-for' );
        if (verifyIp( $ip_addr )) {
            return $ip_addr;
        }
        $ip_addr = $request->getHeaderLine( 'remote-host' );
        if (verifyIp( $ip_addr )) {
            return $ip_addr;
        }
        $ip_addr = $request->getHeaderLine( 'x-real-ip' );
        if (verifyIp( $ip_addr )) {
            return $ip_addr;
        }
        $ip_addr = $request->getServerParams()['remote_addr'] ?? '0.0.0.0';
        if (verifyIp( $ip_addr )) {
            return $ip_addr;
        }
        return '0.0.0.0';
    }
}

/**
 * @param string $mess_id 消息ID
 * @param array $data 数据 根据type传入相应数据
 * @param bool $type 请求类别【true=响应数据，false=原始数据】
 * @param bool $delRedis 是否清空缓存
 */
function wlog ($mess_id = '', array $data = [], $type = true, $delRedis = true)
{
    if ($type) {
        $first_data = redis()->get( 'original_' . $mess_id ) ? json_decode( redis()->get( 'original_' . $mess_id ), true ) : [];
        $req = redis()->get( 'req_' . $mess_id ) ? json_decode( redis()->get( 'req_' . $mess_id ), true ) : [];
        $res = redis()->get( 'req_' . $data['snow_id'] ) ? json_decode( redis()->get( 'req_' . $data['snow_id'] ), true ) : [];
        if ($req && $res) {
            $params['system'] = getAppType();//系统标识
            $request = container()->get( RequestInterface::class );
            $action = $request->getAttribute(\Hyperf\HttpServer\Router\Dispatched::class)->handler->callback;
            list($class, $method) = $action;
            $class = substr(strrchr($class,'\\'),1);
            $params['model_name'] = $data['model_name'] ?? str_replace('Controller','',$class);
            $router_list = admin()->permissions( getAppType() );
            $params['req_router_name'] = empty($data['req_router_name']) ? $router_list[$req['interface']] ?? '' : $data['req_router_name'];
            $params['res_router_name'] = $router_list[$res['interface']] ?? '';
            $params['req_interface'] = $req['interface'];//请求调用的路由
            $params['res_interface'] = $res['interface'];//响应调用的路由
            $params['req_params'] = $req;
            $params['res_params'] = $data;
            $params['req_time'] = !empty( $first_data ) ? $first_data['req_time'] : $req['req_time'];
            $params['res_time'] = date( 'Y-m-d H:i:s' );
            $params['op_id'] = $first_data['op_id'] ?? ($data['op_id'] ?? null);//当前操作的ID值,仅针对审核表单
            $params['original_data'] = $first_data['original_data'] ?? null;//原始数据
            $params['admin_id'] = $req['admin_id'];
            $params['admin_name'] = $req['admin_name'];
            $params['version'] = $req['version'] ?? 'v1';
            $params['remark'] = $data['remark'] ?? '';
            $params['ip'] = getClientIp();
            $ret = container()->get( MessageLogServiceInterface::class )->messageLog( $mess_id, $params );
            if (!empty( $ret ) && $delRedis) {
                redis()->del( 'req_' . $mess_id );
                redis()->del( 'original_' . $mess_id );
            }
            return true;
        }
    } else {
        $data['req_time'] = date( 'Y-m-d H:i:s' );
        return redis()->setex( 'original_' . $mess_id, 30 * 60, json_encode( $data ) );
    }
}

//注：应对同时记录log的情况
function newWLog ($mess_id = '', array $data = [], $type = true, $delRedis = true)
{
    if ($type) {
        $first_data = redis()->get( 'original_' . $mess_id ) ? json_decode( redis()->get( 'original_' . $mess_id ), true ) : [];
        $req = redis()->get( 'req_' . $mess_id ) ? json_decode( redis()->get( 'req_' . $mess_id ), true ) : [];
        $res = redis()->get( 'req_' . $data['snow_id'] ) ? json_decode( redis()->get( 'req_' . $data['snow_id'] ), true ) : [];
        if ($req || $res) {
            $params['system'] = getAppType();//系统标识
            $request = container()->get( RequestInterface::class );
            $action = $request->getAttribute(\Hyperf\HttpServer\Router\Dispatched::class)->handler->callback;
            list($class, $method) = $action;
            $class = substr(strrchr($class,'\\'),1);
            $params['model_name'] = $data['model_name'] ?? str_replace('Controller','',$class);
            $router_list = admin()->permissions( getAppType() );
            $params['req_router_name'] = empty($data['req_router_name']) ? $router_list[$req['interface']] ?? '' : $data['req_router_name'];
            $params['res_router_name'] = (isset($res['interface']) && isset($router_list[$res['interface']])) ? $router_list[$res['interface']] : [];
            $params['req_interface'] = $req['interface'];//请求调用的路由
            $params['res_interface'] = $res['interface'] ?? [];//响应调用的路由
            $params['req_params'] = $req;
            $params['res_params'] = $data;
            $params['req_time'] = !empty( $first_data ) ? $first_data['req_time'] : $req['req_time'];
            $params['res_time'] = date( 'Y-m-d H:i:s' );
            $params['op_id'] = $first_data['op_id'] ?? ($data['op_id'] ?? null);//当前操作的ID值,仅针对审核表单
            $params['original_data'] = $first_data['original_data'] ?? null;//原始数据
            $params['admin_id'] = $req['admin_id'];
            $params['admin_name'] = $req['admin_name'];
            $params['version'] = $req['version'] ?? 'v1';
            $params['remark'] = $data['remark'] ?? '';
            $params['ip'] = getClientIp();
            $ret = container()->get( MessageLogServiceInterface::class )->messageLog( newSnowId(), $params );
            if (!empty( $ret ) && $delRedis) {
                redis()->del( 'req_' . $mess_id );
                redis()->del( 'original_' . $mess_id );
            }
            return true;
        }
    } else {
        $data['req_time'] = date( 'Y-m-d H:i:s' );
        return redis()->setex( 'original_' . $mess_id, 30 * 60, json_encode( $data ) );
    }
}

/**
 * 获取操作的消息日志
 * @param array $params 查询条件【system，msg_id，model_name，op_id，admin_id，req_interface， res_interface】
 *
 */
function getLog (array $params,$page=1, $prePage=2000)
{
    return container()->get( MessageLogServiceInterface::class )->getLogList( $params ,$page,$prePage);
}

//统一输出函数
function parseData ($arr = [], $extend = [])
{
    if (!$arr instanceof DownloadCsv) {
        $return = [
            'msg' => $arr['msg'] ?? '操作成功',
            'code' => $arr['code'] ?? 200,
        ];
        unset( $arr['msg'] );
        unset( $arr['code'] );
        if (empty( $arr['data'] )) {
            $return['result'] = $arr;
        } else {
            $return['result'] = $arr['data'];
        }
        if (!empty( $extend )) {
            foreach ($extend as $k => $v) {
                $return[$k] = $v;
            }
        }

        $return['ip'] = getClientIp();
        $return['request_id'] = getRequestId();
        $return['runtime'] = time();
    }
    return is_array( $arr ) ? container()->get( ResponseInterface::class )->json( $return ) : $arr;
}

/**
 * 导出csv公共函数，按流导出无需生成文件
 * @param array $head
 * @param array $body
 * @param string $file_name
 * @return DownloadCsv
 */
function exportCsv (array $head, array $body, string $file_name): DownloadCsv
{
    $head_keys = array_keys( $head );
    $head_values = array_values( $head );
    $fileData = implode( ',', $head_values ) . "\n";

    if (strpos( $file_name, '.' ) === false) {
        $file_name .= '.csv';
    }

    foreach ($body as $value) {
        $temp_arr = [];
        foreach ($head_keys as $key) {
            $temp_arr[] = $value[$key] ?? '';
        }

        $fileData .= implode(',',$temp_arr) . "\n";
    }
    var_dump("fileData==",$fileData);
//    var_dump('$body==',$body);
    logger()->info('export body：',[$body]);
    $response = new DownloadCsv();
    $content_type = 'text/csv; charset=utf-8';
    return $response->withHeader( 'content-description', 'File Transfer' )
        ->withHeader( 'content-type', $content_type )
        ->withHeader( 'content-disposition', "attachment; filename={$file_name}" )
        ->withHeader( 'content-transfer-encoding', 'binary' )
        ->withHeader( 'pragma', 'public' )
        ->withBody( new \Hyperf\HttpMessage\Stream\SwooleStream( mb_convert_encoding($fileData,"gb2312", "UTF-8") ) );
}

/**
 * 导出Excel文件，参考文档：http://doc.bigoffs.cn/pages/viewpage.action?pageId=71565483
 * @param array $head 表头数据
 * @param array $body 表体数据
 * @param string $file_name 文件名
 * @param array $additional_sheets  新增额外sheet， 数据格式与模板val中字段的格式类似
 * @return DownloadExcel
 */
function exportExcel(array $head, array $body, string $file_name, array $additional_sheets = []): DownloadExcel
{
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();

    // 设置第一个工作表的名称
    $sheet->setTitle($file_name);

    // 获取表头键和值
    $head_keys = array_keys($head);
    $head_values = array_values($head);

    // 检查是否需要添加备注行
    $hasRemarks = false;
    $remarks = [];
    $requiredFields = [];
    $headerValues = [];

    // 处理表头数据
    foreach ($head_values as $index => $value) {
        if (is_array($value)) {
            // 检查是否为必填字段
            if (isset($value['required']) && $value['required'] === true ||
                isset($value['required ']) && $value['required '] === "true") {
                $requiredFields[$index] = true;
            }

            // 检查是否有备注
            if (isset($value['remark'])) {
                $hasRemarks = true;
                $remarks[$index] = $value['remark'];
            }

            // 获取表头
            $headerValues[$index] = $value['header'] ?? '';
        } else {
            $headerValues[$index] = $value;
        }
    }

    // 开始写入数据
    $startRow = 1;

    // 如果有备注，先写入备注行
    if ($hasRemarks) {
        $column = 1;
        foreach ($head_values as $index => $head_value) {
            if (isset($remarks[$index])) {
                $sheet->setCellValueByColumnAndRow($column, $startRow, $remarks[$index]);
            }
            $column++;
        }
        $startRow++;
    }

    // 写入表头
    $column = 1;
    foreach ($headerValues as $index => $head_value) {
        $cellCoordinate = $sheet->getCellByColumnAndRow($column, $startRow)->getCoordinate();
        $sheet->setCellValue($cellCoordinate, $head_value);

        // 设置必填字段的样式（黄背景红字）
        if (isset($requiredFields[$index]) && $requiredFields[$index]) {
            $sheet->getStyle($cellCoordinate)->applyFromArray([
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'color' => ['rgb' => 'FFFF00'], // 黄色背景
                ],
                'font' => [
                    'color' => ['rgb' => 'FF0000'], // 红色字体
                    'bold' => true,
                ],
            ]);
        }

        $column++;
    }

    // 写入数据体
    $row = $startRow + 1;
    foreach ($body as $value) {
        $column = 1;
        foreach ($head_keys as $key) {
            $sheet->setCellValueByColumnAndRow($column, $row, $value[$key] ?? '');
            $column++;
        }
        $row++;
    }

    // 处理额外的工作表
    if (!empty($additional_sheets)) {
        $sheetIndex = 1; // 从第二个工作表开始

        foreach ($additional_sheets as $sheetData) {
            // 检查工作表数据是否包含必要的元素
            if (!isset($sheetData['name']) || !isset($sheetData['head']) || !isset($sheetData['body'])) {
                continue;
            }

            // 创建新的工作表
            $additionalSheet = $spreadsheet->createSheet($sheetIndex);
            $additionalSheet->setTitle($sheetData['name']);
            $sheetIndex++;

            // 获取表头键和值
            $sheet_head = $sheetData['head'];
            $sheet_head_keys = array_keys($sheet_head);
            $sheet_head_values = array_values($sheet_head);

            // 重置变量
            $hasRemarks = false;
            $remarks = [];
            $requiredFields = [];
            $headerValues = [];

            // 处理表头数据
            foreach ($sheet_head_values as $index => $value) {
                if (is_array($value)) {
                    // 检查是否为必填字段
                    if (isset($value['required']) && $value['required'] === true ||
                        isset($value['required ']) && $value['required '] === "true") {
                        $requiredFields[$index] = true;
                    }

                    // 检查是否有备注
                    if (isset($value['remark'])) {
                        $hasRemarks = true;
                        $remarks[$index] = $value['remark'];
                    }

                    // 获取表头显示值
                    $headerValues[$index] = $value['header'] ?? '';
                } else {
                    // 简单格式，直接使用值
                    $headerValues[$index] = $value;
                }
            }

            // 写入数据
            $startRow = 1;

            // 如果有备注，先写入备注行
            if ($hasRemarks) {
                $column = 1;
                foreach ($sheet_head_values as $index => $head_value) {
                    if (isset($remarks[$index])) {
                        $additionalSheet->setCellValueByColumnAndRow($column, $startRow, $remarks[$index]);
                    }
                    $column++;
                }
                $startRow++;
            }

            // 写入表头
            $column = 1;
            $lastColumn = count($headerValues);
            $headerRange = '';

            foreach ($headerValues as $index => $head_value) {
                $cellCoordinate = $additionalSheet->getCellByColumnAndRow($column, $startRow)->getCoordinate();
                $additionalSheet->setCellValue($cellCoordinate, $head_value);

                // 记录表头范围
                if ($column == 1) {
                    $headerRange = $cellCoordinate;
                }
                if ($column == $lastColumn) {
                    $headerRange .= ':' . $cellCoordinate;
                }

                // 设置必填字段的样式（黄背景红字）
                if (isset($requiredFields[$index]) && $requiredFields[$index]) {
                    $additionalSheet->getStyle($cellCoordinate)->applyFromArray([
                        'fill' => [
                            'fillType' => Fill::FILL_SOLID,
                            'color' => ['rgb' => 'FFFF00'], // 黄色背景
                        ],
                        'font' => [
                            'color' => ['rgb' => 'FF0000'], // 红色字体
                            'bold' => true,
                        ],
                    ]);
                }

                $column++;
            }

            // 设置所有表头为粗体和14号字体
            $additionalSheet->getStyle($headerRange)->applyFromArray([
                'font' => [
                    'bold' => true,
                    'size' => 11,  //其实excel默认就是11号字体，设不设无所谓
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'color' => ['rgb' => 'EEEEEE'], // 浅灰色背景
                ],
            ]);

            // 写入数据体
            $row = $startRow + 1;
            $lastRow = $startRow + count($sheetData['body']);

            foreach ($sheetData['body'] as $value) {
                $column = 1;
                foreach ($sheet_head_keys as $key) {
                    $additionalSheet->setCellValueByColumnAndRow($column, $row, $value[$key] ?? '');
                    $column++;
                }
                $row++;
            }

            // 为整个数据区域添加边框
            $dataRange = 'A' . $startRow . ':' . Coordinate::stringFromColumnIndex($lastColumn) . $lastRow;
            $additionalSheet->getStyle($dataRange)->applyFromArray([
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000'],
                    ],
                ],
            ]);
        }
    }

    // 如果文件名没有扩展名，添加.xlsx
    if (strpos($file_name, '.') === false) {
        $file_name .= '.xlsx';
    }

    $writer = new Xlsx($spreadsheet);
    // 将Excel写到临时文件
    $temp_file = tempnam(sys_get_temp_dir(), 'excel_');
    $writer->save($temp_file);

    $fileData = file_get_contents($temp_file);

    // 删除临时文件
    @unlink($temp_file);

    $response = new DownloadExcel();
    $content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    return $response->withHeader('content-description', 'File Transfer')
        ->withHeader('content-type', $content_type)
        ->withHeader('content-disposition', "attachment; filename={$file_name}")
        ->withHeader('content-transfer-encoding', 'binary')
        ->withHeader('pragma', 'public')
        ->withBody(new \Hyperf\HttpMessage\Stream\SwooleStream($fileData));
}

/**
 * Excel下载响应类
 */
class DownloadExcel extends Response
{
}

/**
 * 导出csv公共函数，按流导出无需生成文件
 * @param array $head
 * @param array $body
 * @param string $file_name
 * @return DownloadCsv
 */
function exportTxt ($body, string $file_name)
{
    $response = new Response();
    $content_type = 'application/octet-stream';
    return $response->withHeader( 'content-description', 'File Transfer' )
        ->withHeader( 'content-type', $content_type )
        ->withHeader( 'accept-ranges', 'bytes' )
        ->withHeader( 'accept-length', strlen($body) )
        ->withHeader( 'content-disposition', "attachment; filename={$file_name}" )
        ->withHeader( 'content-transfer-encoding', 'binary' )
        ->withHeader( 'pragma', 'public' )
        ->withBody( new \Hyperf\HttpMessage\Stream\SwooleStream( $body) );
}


function exportCsv2 (array $head_keys, string $head_vals, array $data, string $file_name): DownloadCsv
{
    $fileData = iconv( 'utf-8', 'GBK', ($head_vals) ) . "\n";

    if (strpos( $file_name, '.' ) === false) {
        $file_name .= '.csv';
    }
    foreach ($head_keys as $k1 => $v1) {

        $temp_arr = [];
        foreach ($data as  $k2 => $v2) {
            $temp_arr[] = $v2[$v1] ?? '';
        }
        $fileData .= iconv( 'utf-8', 'GBK', (implode( ',', $temp_arr )) ) . "\n";
    }
    var_dump('$fileData==',$fileData);

    $response = new DownloadCsv();
    $content_type = 'text/csv';
    return $response->withHeader( 'content-description', 'File Transfer' )
        ->withHeader( 'content-type', $content_type )
        ->withHeader( 'content-disposition', "attachment; filename={$file_name}" )
        ->withHeader( 'content-transfer-encoding', 'binary' )
        ->withHeader( 'pragma', 'public' )
        ->withBody( new \Hyperf\HttpMessage\Stream\SwooleStream( $fileData ) );
}

if (!function_exists( 'unzip' )) {
    function unzip ($filePathName, $destination)
    {
        if (!extension_loaded( 'zip' )) throw new ValidateException( 'zip扩展未安装！' );

        // 解压缩
        $zip = new \ZipArchive();
        if ($zip->open( $filePathName )) {
            $zip->extractTo( $destination );// 相对于项目根目录的路径，不可写绝对路径
            $zip->close();
            return true;
        } else {
            return false;
        }
    }
}

if (!function_exists( 'scandirFolder' )) {
    function scandirFolder ($path)
    {
        $list = [];
        $temp_list = scandir( $path );

        foreach ($temp_list as $file) {
            //排除根目录
            if ($file != ".." && $file != "." && substr( $file, 0, 1 ) != ".") {
                if (is_dir( $path . "/" . $file )) {
                    //子文件夹，进行递归
                    $list[$file] = scandirFolder( $path . "/" . $file );
                } else {
                    //根目录下的文件
                    $list[] = $file;
                }
            }
        }
        return $list;
    }
}


/**
 * 图片压缩处理
 * @param string $sFile 源图片路径
 * @param int $iWidth 自定义图片宽度
 * @param int $iHeight 自定义图片高度
 * @return string 压缩后的图片路径
 */
if (!function_exists( 'imgThumb' )) {
    function imgThumb ($sFile, $iWidth = 0, $iHeight = 0)
    {
        //图片公共路径
        $public_path = '';
        //判断该图片是否存在
        if (!file_exists( $public_path . $sFile )) return $sFile;
        //判断图片格式(图片文件后缀)
        $extend = explode( ".", $sFile );
        $attach_fileext = strtolower( $extend[count( $extend ) - 1] );
        if (!in_array( $attach_fileext, array('jpg', 'png', 'jpeg') )) {
            return '';
        }

        //自动填充宽和高
        $fileInfo = getimagesize( $sFile );
        if (isset( $fileInfo[0] ) && isset( $fileInfo[1] )) {
            if (empty( $iWidth )) $iWidth = $fileInfo[0];

            if (empty( $iHeight )) $iHeight = $fileInfo[1];
        } else {
            return '';
        }

        //压缩图片文件名称
        $sFileNameS = str_replace( "." . $attach_fileext, "_" . $iWidth . '_' . $iHeight . '.' . $attach_fileext, $sFile );
        //判断是否已压缩图片，若是则返回压缩图片路径
        if (file_exists( $public_path . $sFileNameS )) {
            return $sFileNameS;
        }

        //生成压缩图片，并存储到原图同路径下
        resizeImage( $public_path . $sFile, $public_path . $sFileNameS, $iWidth, $iHeight );
        if (!file_exists( $public_path . $sFileNameS )) {
            return $sFile;
        }
        return $sFileNameS;
    }
}

if (!function_exists( 'upload' )) {

    /**
     * 上传文件
     * @param $filename
     * @param $filePath
     * @param $toPath
     * @param $uploadType 上传类型 1=合同 , 2=头像 , 3=商品图片 , 4=其他 , 5=临时
     * @return false|string
     * @throws \League\Flysystem\FileExistsException
     */
    function upload ($filename, $filePath, $uploadType)
    {
        logger()->info('upload params',[$filename,$filePath]);
        $pathinfo = my_path_info($filename);
        $filename = $pathinfo['filename'].'-'.uuid($pathinfo['filename']).'.'.$pathinfo['extension'];
        $filesystem = container()->get( \League\Flysystem\Filesystem::class );
        $stream = fopen( $filePath, 'r+' );
        $configService = container()->get( ConfigServiceInterface::class );
        $uploadConfig = $configService->getConfigByKey( 'upload_type' );
        $toPath = $uploadConfig[$uploadType] . '/' . date( 'Y' ) . '/' . date( 'm' ) . '/' . date( 'd' ) . '/';
        $toPathFilename = $toPath . $filename;
        $ret = $filesystem->writeStream(
            $toPathFilename,// 默认是在runtime目录下，可在config/autoload/file.php文件配置
            $stream
        );
        //fclose( $stream );
        if ($ret) {
            if (config( 'file.default' ) == 'local') {
                return config( 'file.storage.local.baseUrl' ) . $toPathFilename;
            } else {
                return 'https://' . env( 'OSS_BUCKET' ) . '.' . env( 'OSS_ENDPOINT' ) . '/' . $toPathFilename;
            }
        } else {
            return false;
        }
    }
}

/**
 * 解析文件路径（可处理中文）
 * @param $filepath
 * @return array
 */
function my_path_info($filepath) {
    $path_parts = array();
    $path_parts ['dirname'] = rtrim(substr($filepath, 0, strrpos($filepath, '/')),"/")."/";
    $path_parts ['basename'] = ltrim(substr($filepath, strrpos($filepath, '/')),"/");
    $path_parts ['extension'] = substr(strrchr($filepath, '.'), 1);
    $path_parts ['filename'] = ltrim(substr($path_parts ['basename'], 0, strrpos($path_parts ['basename'], '.')),"/");
    return $path_parts;
}

if (!function_exists('uuid')) {
    function  uuid($hasSplit=true)
    {
        $uuid = md5(uniqid(mt_rand(), true));
        if($hasSplit){
            $uuid = substr ( $uuid, 0, 8 ) . '-'
                . substr ( $uuid, 8, 4 ) . '-'
                . substr ( $uuid, 12, 4 ) . '-'
                . substr ( $uuid, 16, 4 ) . '-'
                . substr ( $uuid, 20, 12 );
        }
        return $uuid ;
    }
}

if (!function_exists( 'deldir' )) {
    function deldir ($dir)
    {
        //先删除目录下的文件：
        $dh = opendir( $dir );
        while ($file = readdir( $dh )) {
            if ($file != "." && $file != "..") {
                $fullpath = $dir . "/" . $file;
                if (!is_dir( $fullpath )) {
                    unlink( $fullpath );
                } else {
                    deldir( $fullpath );
                }
            }
        }

        closedir( $dh );
        //删除当前文件夹：
        if (rmdir( $dir )) {
            return true;
        } else {
            return false;
        }
    }
}

/**
 * 生成图片
 * @param string $im 源图片路径
 * @param string $dest 目标图片路径
 * @param int $maxwidth 生成图片宽
 * @param int $maxheight 生成图片高
 */
if (!file_exists( 'resizeImage' )) {
    function resizeImage ($im, $dest, $maxwidth, $maxheight)
    {
        $img = getimagesize( $im );
        switch ($img[2]) {
            case 1:
                $im = @imagecreatefromgif( $im );
                break;
            case 2:
                $im = @imagecreatefromjpeg( $im );
                break;
            case 3:
                $im = @imagecreatefrompng( $im );
                break;
        }

        $pic_width = imagesx( $im );
        $pic_height = imagesy( $im );
        $resizewidth_tag = false;
        $resizeheight_tag = false;
        if (($maxwidth && $pic_width > $maxwidth) || ($maxheight && $pic_height > $maxheight)) {
            if ($maxwidth && $pic_width > $maxwidth) {
                $widthratio = $maxwidth / $pic_width;
                $resizewidth_tag = true;
            }

            if ($maxheight && $pic_height > $maxheight) {
                $heightratio = $maxheight / $pic_height;
                $resizeheight_tag = true;
            }

            if ($resizewidth_tag && $resizeheight_tag) {
                if ($widthratio < $heightratio)
                    $ratio = $widthratio;
                else
                    $ratio = $heightratio;
            }


            if ($resizewidth_tag && !$resizeheight_tag)
                $ratio = $widthratio;
            if ($resizeheight_tag && !$resizewidth_tag)
                $ratio = $heightratio;
            $newwidth = $pic_width * $ratio;
            $newheight = $pic_height * $ratio;

            if (function_exists( "imagecopyresampled" )) {
                $newim = imagecreatetruecolor( $newwidth, $newheight );
                imagecopyresampled( $newim, $im, 0, 0, 0, 0, $newwidth, $newheight, $pic_width, $pic_height );
            } else {
                $newim = imagecreate( $newwidth, $newheight );
                imagecopyresized( $newim, $im, 0, 0, 0, 0, $newwidth, $newheight, $pic_width, $pic_height );
            }

            imagejpeg( $newim, $dest );
            imagedestroy( $newim );
        } else {
            imagejpeg( $im, $dest );
        }
    }
}

/**
 * 生成图片名称
 * @param int $spu_id spuId
 * @param string $type 类型 比如 E默认图片 Z上展图 F放大图 X下展图
 * @param int $sort 图片排序 0 - 9
 * @param string $extension 图片后缀 也可以传递图片名称
 * @return string
 */
function generateImageName ($spu_id = 1, $type = 'E', $sort = 0, $extension)
{
    if (strpos( $extension, '.' ) === 0) {
        return md5( $spu_id . $type . $sort . mt_rand( 9999, time() ) ) . '.' . $extension;
    } else {
        return md5( $spu_id . $type . $sort . mt_rand( 9999, time() ) ) . '.' . pathinfo( $extension, PATHINFO_EXTENSION );
    }
}

/**
 * 从excel读取数据并返回
 * @throws \PhpOffice\PhpSpreadsheet\Exception
 * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
 */
if (!function_exists( 'readFromExcel' )) {
    function readFromExcel ($file, $metaData)
    {
        //获取表格的大小，限制上传表格的大小5M
        $file_size = $file->getSize();
        if ($file_size > 5 * 1024 * 1024) {
            throw new ValidateException( '上传文件大小不能超过5M' );
        }

        //限制上传表格类型
        $fileType = $file->getExtension();
        if ($fileType != 'csv') {
            throw new ValidateException( '文件格式只能为 csv 格式!' );
        }

        $data = [];
        if ($file->isValid()) {
            $objReader = IOFactory::createReader( ucfirst( $fileType ) )->setInputEncoding( "GBK" );
            $filePathName = $file->getPathname();// 临时文件的路径+文件名
            $objPHPExcel = $objReader->load( $filePathName );  //$filename可以是上传的表格，或者是指定的表格
            $sheet = $objPHPExcel->getSheet( 0 );   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得最高行数所在行号（总行数）

            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            $zimu = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB'];
            for ($j = 2; $j <= $highestRow; $j++) {
                foreach ($metaData as $k => $v) {
                    $data[$j - 2]['row_num'] = $j; // 该数据在excel第几行
                    $cellValue = $objPHPExcel->getActiveSheet()->getCell( $zimu[$k] . $j )->getValue();
                    $data[$j - 2][$v] = trim( $cellValue );
                }
            }
        }
        return $data;
    }
}

function checkFileEncode (string $filepath, $needEncode = 'UTF-8')
{
    $file = fopen( $filepath, "r" );
    $content = fgets( $file );
    fclose( $file );
    $fileEncoding = mb_detect_encoding( $content, ['UTF-8', 'GBK', 'GB2312'] );
    if ($fileEncoding != $needEncode) {
        file_put_contents( $filepath, mb_convert_encoding( file_get_contents( $filepath ), 'UTF-8', $fileEncoding ) );
    }
//    if(mb_detect_encoding($content,['UTF-8','GBK','GB2312']) != $needEncode){
//        throw new \App\Exception\BusinessException('文件非UTF-8编码，请在模板文件基础上修改！' );
//    }
}

/**
 * 根据传入表头获取导入文件数据
 * @throws \PhpOffice\PhpSpreadsheet\Exception
 * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
 */
if (!function_exists( 'readExcelByHeader' )) {
    function readExcelByHeader ($file, array $header, array $rule = [], array $message = [], $maxSize = 5 * 1024 * 1024)
    {
        //获取表格的大小，限制上传表格的大小5M
        $file_size = $file->getSize();
        if ($file_size > $maxSize) {
            throw new \App\Exception\BusinessException( '上传文件大小不能超过5M' );
        }
        //限制上传表格类型
        $fileType = $file->getExtension();
        $isTxt = false;
        $startRow = 2;
        if($fileType == 'txt'){
            $startRow = 1;
            $isTxt = true;
            $fileType = 'csv';
        }
        if ($fileType != 'csv') {
            throw new \App\Exception\BusinessException( '文件格式只能为 csv 格式!' );
        }

        $data = [];
        if ($file->isValid()) {
            $objReader = IOFactory::createReader( ucfirst( $fileType ) );
            $filePathName = $file->getPathname();// 临时文件的路径+文件名
            checkFileEncode( $filePathName );
            $objPHPExcel = $objReader->load( $filePathName );  //$filename可以是上传的表格，或者是指定的表格
            $sheet = $objPHPExcel->getSheet( 0 );   //excel中的第一张sheet
            $highestRow = $sheet->getHighestRow();       // 取得最高行数所在行号（总行数）
            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            $zimu = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
            //验证表头数量
            $col = 0;
            $columKeyMap = [];
            if($isTxt){
                foreach ($header as $k=>$v) {
                    $columKeyMap[$k] = $zimu[$col];
                    $col++;
                }
            }else{
                foreach ($header as $k => $v) {
                    $cellValue = $objPHPExcel->getActiveSheet()->getCell( $zimu[$col] . 1 )->getValue();
                    if ($v == $cellValue) {
                        $columKeyMap[$k] = $zimu[$col];
                    }
                    $col++;
                }
            }


            if (count( $columKeyMap ) != count( $header )) {
                logger()->info('readExcelByHeader 表头不一致',[$isTxt,$header,$columKeyMap]);
                throw new \App\Exception\BusinessException( '文件表头不正确' );
            }
            //判断是否需要验证数据
            if (empty( $rule )) {
                for ($j = $startRow; $j <= $highestRow; $j++) {
                    foreach ($columKeyMap as $k => $v) {
                        $cellValue = $objPHPExcel->getActiveSheet()->getCell( $v . $j )->getValue();
                        $data[$j][$k] = trim( $cellValue );
                    }
                }
            } else {
                //捕获验证异常
                $j = $startRow;
                try {
                    for (; $j <= $highestRow; $j++) {
                        foreach ($columKeyMap as $k => $v) {
                            $cellValue = $objPHPExcel->getActiveSheet()->getCell( $v . $j )->getValue();
                            $data[$j][$k] = trim( $cellValue );
                        }
                        validate()->make( $data[$j], $rule, $message )->validate();
                    }
                } catch (\Hyperf\Validation\ValidationException $exception) {
                    throw new \App\Exception\BusinessException( sprintf( "行号:%d,%s", $j, $exception->validator->errors()->first() ) );
                }
            }

        }

        return $data;
    }
}

if (!function_exists( 'getExcelHeader' )) {
    function getExcelHeader ($file)
    {
        //限制上传表格类型
        $fileType = $file->getExtension();
        if ($fileType != 'csv') {
            throw new ValidateException( '文件格式只能为 csv 格式!' );
        }

        $data = [];
        if ($file->isValid()) {
            $objReader = IOFactory::createReader( ucfirst( $fileType ) )->setInputEncoding( "GBK" );
            $filePathName = $file->getPathname();// 临时文件的路径+文件名
            $objPHPExcel = $objReader->load( $filePathName );  //$filename可以是上传的表格，或者是指定的表格
            $sheet = $objPHPExcel->getSheet( 0 );   //excel中的第一张sheet
            $zimu = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
            $highestColumn = $sheet->getHighestColumn();   // 取得最高列所在列号（非总列数）
            $columnNum = array_search( $highestColumn, $zimu ) + 1; // 总列数

            //循环读取excel表格，整合成数组。如果是不指定key的二维，就用$data[i][j]表示。
            for ($j = 0; $j < $columnNum; $j++) {
                $cellValue = $objPHPExcel->getActiveSheet()->getCell( $zimu[$j] . '1' )->getValue();
                $data[$j] = trim( $cellValue );
            }
        }

        return $data;
    }
}

/**
 * @param $uploadExcelHeader 上传的excel文件表头数据
 * @param $configMappedHeader 指定的表头数据（一般从配置文件获取）
 */
if (!function_exists( 'checkExcel' )) {
    function checkExcel ($uploadExcelHeader, $configMappedHeader)
    {
        $faild = [];
        if (count( $uploadExcelHeader ) != count( $configMappedHeader )) return false;
        foreach ($uploadExcelHeader as $k => $v) {
            if ($v != $configMappedHeader[$k]) {
                $faild[] = [
                    'upload_value' => $v,
                    'config_value' => $configMappedHeader[$k],
                ];
            }
        }
        return ['failed_data' => $faild];
    }
}

if (!function_exists( 'exportToExcel' )) {
    /**
     * 导出数据到excel文件
     * @param array $header 输出文件的表头（一维数组）
     * @param array $data 要输出的数据（二维数组）
     * @param string $filename 输出文件名
     * @return false|string 返回上传后的url
     */
    function exportToExcel (array $header = [], array $data = [], $filename = '',$header_types=[])
    {
        if (empty( $data )) return false;
        $filename .= date( 'YmdHis' ) . '.xls';

        $writer = new \XLSXWriter();
        $sheet1 = 'sheet1';

        if(!empty($header_types)){
            $writer->writeSheetHeader( $sheet1, $header_types );
        }
        $final = [];
        if (is_assoc( $header )) { //表头数据带所引，则根据表头数据生成导出数据
            if (!empty( $header ) && empty($header_types)) {
                $writer->writeSheetRow( $sheet1, array_values( $header ) );
            }
            $headerKeys = array_keys( $header );
            foreach ($data as $value) {
                $row = [];
                foreach ($headerKeys as $key) {
                    $row[] = $value[$key];
                }
                $writer->writeSheetRow( $sheet1, $row );
            }
        } else {
            if (!empty( $header )) {
                $final[] = $header;
            }
            $data = array_merge( $final, $data );
            logger()->debug('code_list',[$data]);
            foreach ($data as $value) {
                if(is_array($value)){
                    $writer->writeSheetRow( $sheet1, $value );
                }else{
                    $writer->writeSheetRow( $sheet1, [$value] );
                }

            }
        }

        // 临时路径
        $basdDir = 'runtime/';
        $filePath = $basdDir . $filename;
        // 输出二进制流到本地文件
        $writer->writeToFile( $filePath );

        $url = upload( $filename, $filePath, 5 );

        unlink( $filePath );

        return $url;
    }
}

/**
 * 字符转换（utf-8 => GBK）
 * @param $data
 * @return false|string
 */
function utf8ToGbk ($data)
{
    return iconv( 'utf-8', 'GBK', $data );
}


if (!function_exists( 'is_assoc' )) {
    /**
     * 判断数组是否为所引数组
     * @param $data
     * @return bool
     */
    function is_assoc (array $data)
    {
        return !empty($data) && !isset( $data[0] );
    }
}


if (!function_exists( 'yuan2fen' )) {
    /**
     * 转换价格到元, 保留 2 位小数
     *
     * @param $yuan
     *
     * @return string
     */
    function yuan2fen ($yuan)
    {
        return (int)bcmul( str_replace( ",", '', $yuan ), 100, 0 );
    }
}

if (!function_exists( 'fen2yuan' )) {
    /**
     * 转换价格到分
     *
     * @param $fen
     *
     * @return int
     */
    function fen2yuan ($fen)
    {
        return number_format( floatval( bcdiv( $fen, 100, 2 ) ), 2 );
    }
}

if (!function_exists( 'numToRmb' )) {
    /**
     * 人民币小写转大写
     * @param string $number 数值
     * @param string $int_unit 币种单位，默认"元"，有的需求可能为"圆"
     * @param bool $is_round 是否对小数进行四舍五入
     * @param bool $is_extra_zero 是否对整数部分以0结尾，小数存在的数字附加0,比如1960.30，
     *             有的系统要求输出"壹仟玖佰陆拾元零叁角"，实际上"壹仟玖佰陆拾元叁角"也是对的
     * @return string
     */
    function numToRmb ($number = 0, $int_unit = '元', $is_round = true, $is_extra_zero = false)
    {
        // 将数字切分成两段
        $parts = explode( '.', $number, 2 );
        $int = isset( $parts[0] ) ? strval( $parts[0] ) : '0';
        $dec = isset( $parts[1] ) ? strval( $parts[1] ) : '';

        // 如果小数点后多于2位，不四舍五入就直接截，否则就处理
        $dec_len = strlen( $dec );
        if (isset( $parts[1] ) && $dec_len > 2) {
            $dec = $is_round
                ? substr( strrchr( strval( round( floatval( "0." . $dec ), 2 ) ), '.' ), 1 )
                : substr( $parts[1], 0, 2 );
        }

        // 当number为0.001时，小数点后的金额为0元
        if (empty( $int ) && empty( $dec )) {
            return '零';
        }

        // 定义
        $chs = array('0', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖');
        $uni = array('', '拾', '佰', '仟');
        $dec_uni = array('角', '分');
        $exp = array('', '万');
        $res = '';

        // 整数部分从右向左找
        for ($i = strlen( $int ) - 1, $k = 0; $i >= 0; $k++) {
            $str = '';
            // 按照中文读写习惯，每4个字为一段进行转化，i一直在减
            for ($j = 0; $j < 4 && $i >= 0; $j++, $i--) {
                $u = $int[$i] > 0 ? $uni[$j] : ''; // 非0的数字后面添加单位
                $str = $chs[$int[$i]] . $u . $str;
            }
            //echo $str."|".($k - 2)."<br>";
            $str = rtrim( $str, '0' );// 去掉末尾的0
            $str = preg_replace( "/0+/", "零", $str ); // 替换多个连续的0
            if (!isset( $exp[$k] )) {
                $exp[$k] = $exp[$k - 2] . '亿'; // 构建单位
            }
            $u2 = $str != '' ? $exp[$k] : '';
            $res = $str . $u2 . $res;
        }

        // 如果小数部分处理完之后是00，需要处理下
        $dec = rtrim( $dec, '0' );

        // 小数部分从左向右找
        if (!empty( $dec )) {
            $res .= $int_unit;

            // 是否要在整数部分以0结尾的数字后附加0，有的系统有这要求
            if ($is_extra_zero) {
                if (substr( $int, -1 ) === '0') {
                    $res .= '零';
                }
            }

            for ($i = 0, $cnt = strlen( $dec ); $i < $cnt; $i++) {
                $u = $dec[$i] > 0 ? $dec_uni[$i] : ''; // 非0的数字后面添加单位
                $res .= $chs[$dec[$i]] . $u;
            }
            $res = rtrim( $res, '0' );// 去掉末尾的0
            $res = preg_replace( "/0+/", "零", $res );
// 替换多个连续的0
        } else {
            $res .= $int_unit . '整';
        }
        return $res;
    }
}

if (!function_exists( 'getAppType' )) {
    /**
     * 获取系统类型
     * @return string
     */
    function getAppType ()
    {
        $appType = env( 'APP_TYPE' );
        if (empty( $appType )) {
            throw new \App\Exception\BusinessException( '无法获取系统类型' );
        }
        return $appType;
    }
}


if (!function_exists( 'array_repeat_keys' )) {
    /**
     * 找出数组中所有重复的键
     * @return $array
     */
    function array_repeat_keys ($value,$array)
    {
        $result = [];
        foreach ($array as $k => $v) {
            if ($v == $value) {
                array_push($result,$k);
            }
        }
        return $result;
    }
}

if (!function_exists( 'exportMoreSheetExcel' )) {
    /**
     * 导出多sheet数据
     * @param array $headers
     * @param array $data
     * @param string $filename
     * @return false|string
     * @throws \League\Flysystem\FileExistsException
     */
    function exportMoreSheetExcel ($headers = [], $data = [], $filename = '')
    {
        /*//参数实例
        $headers = [
            'sheet1' => ['姓名', '分数'],
            'sheet2' => ['姓名', '班级'],
        ];
        $data = [
            'sheet1' => [
                ['张三', 99],
                ['马六', 99]
            ],
            'sheet2' => [
                ['李四', 'A班'],
                ['王五', 'A班']
            ],
        ];*/
        if (empty( $data )) return false;
        $filename .= date( 'YmdHis' ) . '.xls';

        $writer = new \XLSXWriter();
        foreach ($headers as $sheet => $header) {
            if (!is_assoc( $header )) { //表头数据带所引，则根据表头数据生成导出数据
                $writer->writeSheetRow( $sheet, $header );
                foreach ($data[$sheet] as $value) {
                    $writer->writeSheetRow( $sheet, $value );
                }
            }
        }
        $basdDir = 'runtime/';
        $filePath = $basdDir . $filename;
        //输出二进制流到本地文件
        $writer->writeToFile( $filePath );
        $url = upload( $filename, $filePath, 5 );
        unlink( $filePath );
        return $url;
    }
}


//csv类
class DownloadCsv extends Response
{
}

if (!function_exists( 'checkUniqueCodeFormat' )) {

    /**
     * 检查店内码格式
     * @param $unique_code
     * @return string
     */
    function checkUniqueCodeFormat($unique_code)
    {
        if (!preg_match('/^[A-Z]{2,3}(\d{8,10})$/i', $unique_code)) {
            return '店内码格式不正确';
        }

        return '';
    }
}

/**
 * 获取所有省市区数据
 */
function getAreaAll() {
    return container()->get( \App\JsonRpc\AreaServiceInterface::class ) ->getAreaAll();
}

/**
 * 获取所有仓库
 */

/*function getWarehouses() {
    $result = container()->get( \App\JsonRpc\WarehouseServiceInterface::class ) ->getWarehouses();
    return $result;
}*/

if (!function_exists( 'readTxt' )) {
    function readTxt ($file,array $metaData)
    {
        if (!is_array($metaData)) throw new BusinessException('元数据必须是数组');
        //限制上传表格类型
        $fileType = $file->getExtension();
        if ($fileType != 'txt') {
            throw new ValidateException( '文件格式必须为 txt 格式!' );
        }
        if ($file->isValid()) {
            //	读取文件
            $filePathName = $file->getPathname();// 临时文件的路径+文件名
            $txtOpen = fopen($filePathName, "r") or die("Unable to open file!");
            $txtContent =  fread($txtOpen,filesize($filePathName));
            $txtContent = convertStrType($txtContent,'TOSBC');// 转换为半角
            fclose($txtOpen);
            $txtContent = array_filter(explode(PHP_EOL,trim(str_replace(' ','',$txtContent))));

            $result = [];
            foreach ($txtContent as $k1 => $txt) {
                $txt = str_replace(PHP_EOL, '', $txt);// 去除字符串中的换行符

                // 空行，字符串长度都为1，跳过空行
                if (strlen($txt) == 1) {
                    continue;
                }
                if (!empty($txt)) {
                    $txtArr = explode(',',$txt);
                    if (count($txtArr) != count($metaData)) throw new BusinessException('上传的文件内容格式与系统配置不一致，请检查后再试');

                    foreach ($txtArr as $k2 => $item) {
                        $result[$k1]["row_num"] = $k1 + 1;
                        $result[$k1][$metaData[$k2]] = trim($item);
                    }
                }

            }
        }

        return $result;
    }
}

/**
 * 根据模板名，获取详情
 * @param string $tpl_name
 * @param int $type
 * @return array $tpl_ret
 */
function getTemplateInfo($tpl_name='',$type=1){
    $tpl_ret = container()->get( \App\JsonRpc\TemplateSetServiceInterface::class )->getTemplate($tpl_name);
    if(empty($tpl_ret)){
        return false;
    }
    $table_tpl_list = json_decode($tpl_ret['table_header'],true);
    return $table_tpl_list[$type];
}

/**
 * 查找二维数组，并返回相应键名
 * @param $arr
 * @param $value
 */
function array_search2 ($arr,$key,$value) {
    $result = false;
    foreach ($arr as $k => $v) {
        if ($value == $v[$key]) {
            $result = $k;
            break;
        }
    }
    return $result;
}

/**
 * 数组去重
 */
if (!function_exists( 'array_unique2' )) {
    function array_unique2 (array $data)
    {
        return array_keys(array_flip($data));
    }
}

/**
 * 根据value值删除数组元素
 * @param $value
 * @param $arr
 */
function removeArrValue($value,&$arr) {
    $key=array_search($value ,$arr);
    array_splice($arr,$key,1);
}

/**
 * 查找二维数组中指定键值的索引，并返回相应键名列表
 * @param $arr
 * @param $value
 */
function array_search3 ($arr,$key,$value)
{
    $result = [];
    foreach ($arr as $k => $v) {
        if ($value == $v[$key]) {
            array_push($result, $k);
        }
    }
    return $result;
}

/** 查找模板文件
 * @param string $t_key  模板标识名,配置文件或数据库中对应的模块标识名
 * @param  string $t_names  上传模板的表头数据，以逗号分隔
 * @return array | bool 成功则返回模板数据，失败返回false
 */
function findTemplate($t_key,$t_names){
    $tpl_ret = container()->get( \App\JsonRpc\TemplateSetServiceInterface::class )->getTemplate($t_key);
    if(empty($tpl_ret)){
        return false;
    }
    $table_header_list = json_decode($tpl_ret['table_header'],true);
    foreach ($table_header_list as $key=>$table_header){
        if($table_header['val']==$t_names){
            $table_header['type'] = $key;
            return $table_header;
        }
    }
    return false;
}

//对象数组转换成普通数组
function object_array($array)
{
    if (is_object($array)) {
        $array = (array)$array;
    }
    if (is_array($array)) {
        foreach ($array as $key => $value) {
            $array[$key] = object_array($value);
        }
    }
    return $array;
}

/**
 * 获取当前用户id
 * @return int
 */
function getCurUserId () {
    $session = container() -> get(\Hyperf\Contract\SessionInterface::class);
    $userInfo = $session->get('userInfo');
    return intval($userInfo['uid']);
}
/**
 * 获取当前用户有权限的仓库id
 */
function getAuthWIds() {
    return AdminService::organizeWareHouseData(getCurUserId ());
}

function addUniqueCodeLog($mess_id = '', array $data = [], $type = true)
{
    $ret = container()->get( UniqueCodeLogServiceInterface::class )->operationLog( $mess_id, $data );
    return true;
}

function getUniqueCodeLog(array $params,$page=1, $perPage=2000)
{
    return container()->get( UniqueCodeLogServiceInterface::class )->getOperationLogList( $params,$page, $perPage );
}

/**
 * 字符串半角和全角间相互转换
 * @param string $str 待转换的字符串
 * @param int  $type TODBC:转换为全角；TOSBC，转换为半角
 * @return string 返回转换后的字符串
 */
function convertStrType($str, $type) {

    $dbc = array(
        '０' , '１' , '２' , '３' , '４' ,
        '５' , '６' , '７' , '８' , '９' ,
        'Ａ' , 'Ｂ' , 'Ｃ' , 'Ｄ' , 'Ｅ' ,
        'Ｆ' , 'Ｇ' , 'Ｈ' , 'Ｉ' , 'Ｊ' ,
        'Ｋ' , 'Ｌ' , 'Ｍ' , 'Ｎ' , 'Ｏ' ,
        'Ｐ' , 'Ｑ' , 'Ｒ' , 'Ｓ' , 'Ｔ' ,
        'Ｕ' , 'Ｖ' , 'Ｗ' , 'Ｘ' , 'Ｙ' ,
        'Ｚ' , 'ａ' , 'ｂ' , 'ｃ' , 'ｄ' ,
        'ｅ' , 'ｆ' , 'ｇ' , 'ｈ' , 'ｉ' ,
        'ｊ' , 'ｋ' , 'ｌ' , 'ｍ' , 'ｎ' ,
        'ｏ' , 'ｐ' , 'ｑ' , 'ｒ' , 'ｓ' ,
        'ｔ' , 'ｕ' , 'ｖ' , 'ｗ' , 'ｘ' ,
        'ｙ' , 'ｚ' , '－' , '　' , '：' ,
        '．' , '，' , '／' , '％' , '＃' ,
        '！' , '＠' , '＆' , '（' , '）' ,
        '＜' , '＞' , '＂' , '＇' , '？' ,
        '［' , '］' , '｛' , '｝' , '＼' ,
        '｜' , '＋' , '＝' , '＿' , '＾' ,
        '￥' , '￣' , '｀'

    );

    $sbc = array( //半角
        '0', '1', '2', '3', '4',
        '5', '6', '7', '8', '9',
        'A', 'B', 'C', 'D', 'E',
        'F', 'G', 'H', 'I', 'J',
        'K', 'L', 'M', 'N', 'O',
        'P', 'Q', 'R', 'S', 'T',
        'U', 'V', 'W', 'X', 'Y',
        'Z', 'a', 'b', 'c', 'd',
        'e', 'f', 'g', 'h', 'i',
        'j', 'k', 'l', 'm', 'n',
        'o', 'p', 'q', 'r', 's',
        't', 'u', 'v', 'w', 'x',
        'y', 'z', '-', ' ', ':',
        '.', ',', '/', '%', ' #',
        '!', '@', '&', '(', ')',
        '<', '>', '"', '\'','?',
        '[', ']', '{', '}', '\\',
        '|', '+', '=', '_', '^',
        '￥','~', '`'

    );
    if($type == 'TODBC'){
        return str_replace( $sbc, $dbc, $str ); //半角到全角
    }elseif($type == 'TOSBC'){
        return str_replace( $dbc, $sbc, $str ); //全角到半角
    }else{
        return $str;
    }



}

/**
 * 转换二维数组中指定字段为大写或小写
 * @param $arr
 * @param $keyTypeMaps // 示例：['unique_code' => 'to_upper','shelf_code' => 'to_lower']
 */
function arrayValStrConvert(array &$arr,array $keyTypeMaps) {
    foreach ($arr as &$v) {
        if (is_array($v)) {
            foreach ($keyTypeMaps as $key => $type) {
                if ($type == 'to_upper') {
                    $v[$key] = strtoupper($v[$key]);
                }
            }
        }
        if (is_array($v)) {
            foreach ($keyTypeMaps as $key => $type) {
                if ($type == 'to_lower') {
                    $v[$key] = strtolower($v[$key]);
                }
            }
        }
    }
}

/**
 * 生成snow_id（针对于wlog方法）
 * @return string
 */
function generateSnowId()
{
    $snow_id = newSnowId();
    $session = container()->get(SessionInterface::class);
    $request = container()->get(RequestInterface::class);

    $admin_info = $session->get('userInfo');

    $post = $request->all();
    $post['interface'] = $request->getUri()->getPath();
    $post['admin_id'] = $admin_info['uid'];
    $post['admin_name'] = $admin_info['nickname'];
    $post['req_time'] = date('Y-m-d H:i:s');

    redis()->setex('req_' . $snow_id, 30 * 60, json_encode($post));

    return $snow_id;
}

/**
 * 生成snow_id
 * @return string
 */
function newSnowId()
{
    $snow_id = container()->get(IdGeneratorInterface::class)->generate();
    return (string)$snow_id;
}

/**
 * 当前时间
 */
if (!function_exists('currentTime')) {
    function currentTime()
    {
        return date('Y-m-d H:i:s');
    }
}

/**
 * 多字段搜索
 * @param array $arr 待搜索集合
 * @param array $kvs 多字段kv键值对
 */
function searchItemByMultiFields(array $arr,array $kvs)
{
    $result = array_filter($arr,function ($item) use ($kvs) {
        $conditionResult = [];
        foreach ($kvs as $k => $v) {
            if ($item[$k] == $v) {
                array_push($conditionResult,1);
            }
        }
        if (count($conditionResult) == count($kvs)) {
            return $item;
        }
        return false;
    });
    if ($result) {
        return [
            'key' => array_keys($result)[0],
            'val' => array_values($result)[0],
        ];
    } else {
        return false;
    }
}

function ws() {
    return container() -> get(ServerFactory::class) -> getServer() -> getServer();
}

function returnData($code = \App\Constants\ResponseCode::SUCCESS, $msg = '操作成功', $data = [], $extend = [])
{
    $return = [
        'msg'  => $msg,
        'code' => $code,
    ];
    if (!empty($data)) {
        $return['data'] = $data;
    }
    if (!empty($extend)) {
        foreach ($extend as $k => $v) {
            $return[$k] = $v;
        }
    }
    return $return;
}


// 售后单展示给小程序用户端的日志
function serviceLog(string $operation_id, array $data)
{
    container()->get(\App\JsonRpc\OrderServiceOperationLogServiceInterface::class)->operationLog($operation_id, $data);
}
/**
 * 批量给数组转换成大写
 * @param $input
 * @param $ucase
 * @return array
 */
function array_change_value_case($input, $ucase = CASE_UPPER)

{
    $case = $ucase;
    $narray = array();
    if (!is_array($input))
    {
        return $narray;
    }

    foreach ($input as $key => $value)
    {
        if (is_array($value))
        {
            $narray[$key] = array_change_value_case($value, $case);
            continue;
        }
        $narray[$key] = ($case == CASE_UPPER ? strtoupper($value) : strtolower($value));
    }
    return $narray;

}

// 二维数组，按照指定字段排序
function arraySort($array,$keyName,$sort='asc') {
    $newArr = $valArr = array();
    foreach ($array as $key=>$value) {
        $valArr[$key] = $value[$keyName];
    }
    ($sort == 'asc') ?  asort($valArr) : arsort($valArr);
    reset($valArr);
    foreach($valArr as $key=>$value) {
        $newArr[$key] = $array[$key];
    }
    return $newArr;
}

/**
 * 将货架排合并货架不同的A、B、C、D等面
 * @param $shelfLines ['KF-01A','CC-01C']
 * @return array
 */
function makeShelfLinesMerged ($shelfLines) {
    // 针对货架号，将货架排的AB面何为一个货架排：例如 AA-01A，AA-01B，合并为AA-01分组，并将对应数据放在其分组下
    $result = collect($shelfLines) -> groupBy(function ($shelfLine,$key) {
        // 正则取出货架排
        $shelfLineArr = explode('-',$shelfLine);
        $shelfArea = $shelfLineArr[0];
        $shelfLine = $shelfLineArr[1];
        // 取出货架排的数字部分
        $shelfLineSubStr = preg_replace('/\D/s', '', $shelfLine);
        // 重新组合货架排作为分组key
        return $shelfArea.'-'.$shelfLineSubStr;
    }) -> toArray();
    return array_keys($result);
}

/**
 * 将货架排合并货架不同的A、B、C、D等面
 * @param $shelfLineData
 * @return array
 */
function makeMergedShelfLine ($shelfLineData)
{
    // 针对货架号，将货架排的AB面何为一个货架排：例如 AA-01A，AA-01B，合并为AA-01分组，并将对应数据放在其分组下
    $shelfLineData = collect($shelfLineData)->groupBy(function ($item, $key) {
        // 正则取出货架排
        $shelfLineArr = explode('-', $item['shelf_line']);
        $shelfArea = $shelfLineArr[0];
        $shelfLine = $shelfLineArr[1];
        // 取出货架排的数字部分
        $shelfLineSubStr = preg_replace('/\D/s', '', $shelfLine);
        // 重新组合货架排作为分组key
        return $shelfArea . '-' . $shelfLineSubStr;
    })->all();

    // 重组货架排数据
    $newShelfData = [];
    foreach ($shelfLineData as $subShelfLine => $shelfLineChildren) {
        $pItem = [
            'name' => $subShelfLine,
            'value' => $subShelfLine,
            'children' => [],
        ];
        foreach ($shelfLineChildren as $shelfLineChild) {
            array_push($pItem['children'], [
                'name' => $shelfLineChild['shelf_line'],
                'value' => $shelfLineChild['shelf_line'],
            ]);
        }
        array_push($newShelfData, $pItem);
    }

    return $newShelfData;
}

/**
 * 获取幂等性校验令牌
 * @param $adminId
 * @return string
 */
function getIdempotenceToken ($adminId) {
    $token = uuid();
    $redis = redis();
    $key = $adminId.'_wms_'.\App\Constants\PublicCode::IDEMPOTENCE_TOKEN_KEY;
    $redis -> set($key,$token);
    $redis -> expire($key,120); // 120s秒后自动删除
    return $token;
}

/**
 * 删除幂等性校验令牌
 * @param $adminId
 */
function delIdempotenceToken($adminId)
{
    redis() -> del($adminId.'_wms_'.\App\Constants\PublicCode::IDEMPOTENCE_TOKEN_KEY);
}

function trim_val($val){
    return trim($val);
}

// 一维转二维 用于方便前端解析
function splitArray($data = [])
{
    $new_data = [];
    if ($data) {
        foreach ($data as $key => $value) {
            $new_data[] = [
                'key' => (string)$key,
                'value' => (string)$value
            ];
        }
    }
    return $new_data;
}

if (!function_exists('ossUpload')) {
    /**
     * oss上传文件
     * @param $filename
     * @param $filePath
     * @param $uploadType @上传类型 1=合同 , 2=头像 , 3=商品图片 , 4=其他 , 5=临时
     * @param $is_base64
     * @return false
     */
    function ossUpload($filename, $filePath, $uploadType, $is_base64 = false)
    {
        $accessKeyId = env('OSS_ACCESS_ID');
        $accessKeySecret = env('OSS_ACCESS_SECRET');
        $endpoint = env('OSS_ENDPOINT');
        $bucket = env('OSS_BUCKET');
        $pathinfo = pathinfo($filename);
        $options = [];
        //兼容结算单下载文件名 临时文件直接原名上传
        if ($uploadType == 5) {
            $filename = $pathinfo['filename'] . '.' . $pathinfo['extension'];
        } else {
            $filename = uuid() . '.' . $pathinfo['extension'];
            if ($pathinfo['extension'] == 'txt') {
                $options[\OSS\OssClient::OSS_CONTENT_TYPE] = 'application/octet-stream';
            }
        }
        $object = 'upload/' . date("Y-m-d") . '/' . $filename;    // 文件名称
        try {
            $ossClient = new \OSS\OssClient($accessKeyId, $accessKeySecret, $endpoint);
            $ossClient->createBucket($bucket, \OSS\OssClient::OSS_ACL_TYPE_PUBLIC_READ);
            if ($is_base64) {
                $rel = $ossClient->putObject($bucket, $object, $filePath, $options);
            } else {
                $rel = $ossClient->uploadFile($bucket, $object, $filePath, $options);
            }
            return str_replace("http", "https", $rel["info"]['url']);
        } catch (\OSS\Core\OssException $e) {
            logger()->error('上传文件:'.$filename.'出错,原因：'.$e->getMessage(),[$filename, $filePath, $uploadType, $is_base64 ]);
            return false;
        }
    }
}

/**
 * 判断是否重复请求
 * @param $key
 * @param $expire 请求放行等待时间
 * @return bool
 */
function isRepeatSubmit($key, $expire=30) {
    $redis = redis();
    $flag = $redis->get($key);
    // 存在，表示重复请求，返回true
    if($flag){
        return true;
    }
    $redis->setex($key, $expire,1);
    return false;
}

/**
 * 获取管理员关联品牌
 * @param int $adminId
 * @return mixed
 */
function getBrandIdsByAdminId(int $adminId){
    return \App\Library\Facades\AdminService::getBrandIdsByAdminId($adminId);
}

/**
 * 根据管理员品牌获取供应商
 * @param int $adminId
 * @param int $isCheckStatus
 * @return mixed
 */
function getSupIdsByAdminBrand(int $adminId, int $isCheckStatus = 1)
{
    return \App\Library\Facades\ContractService::getSupIdsByAdminBrand($adminId, $isCheckStatus);
}

function isDate($date) {
    return strtotime($date) !== false;
}

function getLoginUrl(ServerRequestInterface $request)
{
    $port = env('APP_PORT',80);
    logger()->debug('login_url',['port'=>$port,'host'=>$request->getUri()->getHost(),$request->fullUrl()]);
    if($port != 80){
        $port = ':'.$port;
    }else{
        $port = '';
    }
    return env('LOGIN_URL','https://x-erp.bigoffs.cn/#/login/login').'?r=//'.urlencode($request->getUri()->getHost().$port);
}
/**
 * 导出数据到excel文件
 * @param array $header 输出文件的表头（一维数组）
 * @param array $data 要输出的数据（二维数组）
 * @param string $filename 输出文件名
 * @return false|string 返回上传后的url
 */
function exportToExcelSpread (array $header = [], array $data = [], $filename = '',$header_types=[], $image_field_names = [])
{
    if (empty( $data )) return false;
    $filename .= date( 'YmdHis' ) . '.xlsx';

    // 使用 PhpSpreadsheet 替代 XLSXWriter 以支持图片嵌入
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('sheet1');

    $row = 1;
    $column = 1;

    if (is_assoc( $header )) { //表头数据带所引，则根据表头数据生成导出数据
        // 写入表头
        if (!empty( $header_types )) {
            // 如果有header_types，使用它作为表头
            $headerValues = array_keys( $header_types );
            foreach ($headerValues as $headerValue) {
                $cellCoordinate = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($column) . $row;
                $cell = $sheet->getCell($cellCoordinate);
                $cell->setValue($headerValue);
                $column++;
            }
            $row++;
        } elseif (!empty( $header )) {
            // 否则使用header
            $headerValues = array_values( $header );
            foreach ($headerValues as $headerValue) {
                $cellCoordinate = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($column) . $row;
                $cell = $sheet->getCell($cellCoordinate);
                $cell->setValue($headerValue);
                $column++;
            }
            $row++;
        }

        $headerKeys = array_keys( $header );
        foreach ($data as $value) {
            $column = 1;
            foreach ($headerKeys as $key) {
                $cellCoordinate = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($column) . $row;
                $cell = $sheet->getCell($cellCoordinate);

                // 判断是否是图片字段（根据字段名包含img判断）
                if (!empty($image_field_names) && in_array($key, $image_field_names) && !empty($value[$key])) {
                    try {
                        // 验证URL格式
                        if (!filter_var($value[$key], FILTER_VALIDATE_URL)) {
                            throw new \Exception('无效的图片URL格式');
                        }

                        $urlParts = parse_url($value[$key]);
                        if (!$urlParts || !isset($urlParts['host'])) {
                            throw new \Exception('无法解析图片URL');
                        }

                        // 确定端口
                        $port = $urlParts['port'] ?? (($urlParts['scheme'] === 'https') ? 443 : 80);
                        $ssl = ($urlParts['scheme'] === 'https');

                        $tempFile = tempnam(sys_get_temp_dir(), 'img_') . '.tmp';

                        // 使用 Swoole 协程 HTTP 客户端
                        $client = new \Swoole\Coroutine\Http\Client(
                            $urlParts['host'],
                            $port,
                            $ssl
                        );
                        $client->set([
                            'timeout' => 15,
                            'headers' => [
                                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                            ]
                        ]);

                        $path = ($urlParts['path'] ?? '/') . (isset($urlParts['query']) ? '?' . $urlParts['query'] : '');
                        $success = $client->download($tempFile, $path);

                        if (!$success || $client->statusCode !== 200) {
                            throw new \Exception('图片下载失败: HTTP ' . ($client->statusCode ?? 'Unknown'));
                        }

                        // 验证下载的文件是否为有效图片
                        $imageInfo = @getimagesize($tempFile);
                        if (!$imageInfo) {
                            throw new \Exception('下载的文件不是有效的图片格式');
                        }

                        // 创建图片对象并插入到Excel
                        $drawing = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                        $drawing->setPath($tempFile);
                        $drawing->setCoordinates($cell->getCoordinate());
                        $drawing->setWorksheet($sheet);

                        // 设置图片大小
                        $drawing->setHeight(80);
                        $drawing->setWidth(80);

                        // 调整单元格尺寸
                        $sheet->getColumnDimension($cell->getColumn())->setWidth(15);
                        $sheet->getRowDimension($row)->setRowHeight(90);

                        // 清理临时文件
                        if (file_exists($tempFile)) {
                            unlink($tempFile);
                        }

                    } catch (\Exception $e) {
                        logger()->error('图片处理失败：' . $e->getMessage() . ' URL:' . $value[$key]);
                        // 图片处理失败时，在单元格中显示URL或提示信息
                        $cell->setValue('图片加载失败: ' . $value[$key]);

                        // 清理可能存在的临时文件
                        if (isset($tempFile) && file_exists($tempFile)) {
                            unlink($tempFile);
                        }
                    } finally {
                        if (isset($client)) {
                            $client->close();
                        }
                    }
                } else {
                    // 普通文本内容
                    $cell->setValue($value[$key] ?? '');
                }
                $column++;
            }
            $row++;
        }
    } else {
        // 写入表头
        if (!empty( $header )) {
            foreach ($header as $headerValue) {
                $cellCoordinate = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($column) . $row;
                $cell = $sheet->getCell($cellCoordinate);
                $cell->setValue($headerValue);
                $column++;
            }
            $row++;
        }

        // 写入数据
        foreach ($data as $value) {
            $column = 1;
            if(is_array($value)){
                foreach ($value as $cellValue) {
                    $cellCoordinate = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($column) . $row;
                    $cell = $sheet->getCell($cellCoordinate);
                    $cell->setValue($cellValue);
                    $column++;
                }
            } else {
                $cellCoordinate = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($column) . $row;
                $cell = $sheet->getCell($cellCoordinate);
                $cell->setValue($value);
            }
            $row++;
        }
    }

    // 临时路径
    $basdDir = 'runtime/';
    $filePath = $basdDir . $filename;

    // 使用 Xlsx writer 保存文件
    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    $writer->save($filePath);

    $url = upload( $filename, $filePath, 5 );

    unlink( $filePath );

    return $url;
}